"""
Discord Ticket Monitor - Main Application
"""

import asyncio
import threading
import tkinter as tk
from tkinter import messagebox
import sys
import logging

from discord_monitor import DiscordMonitor
from gui_overlay import TicketOverlay
from notifications import NotificationManager
import config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('discord_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DiscordTicketMonitorApp:
    def __init__(self):
        self.discord_monitor = None
        self.gui_overlay = None
        self.notification_manager = NotificationManager()
        self.discord_loop = None
        self.discord_thread = None
        self.running = False
        
    def on_ticket_detected(self, ticket_data):
        """Callback function when a ticket is detected"""
        logger.info(f"New ticket detected: {ticket_data['content'][:50]}...")
        
        # Add ticket to GUI (thread-safe)
        if self.gui_overlay:
            self.gui_overlay.root.after(0, lambda: self.gui_overlay.add_ticket(ticket_data))
        
        # Send notifications
        self.notification_manager.notify_new_ticket(ticket_data)
    
    def start_discord_monitor(self):
        """Start Discord monitoring in a separate thread"""
        def run_discord():
            self.discord_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.discord_loop)
            
            try:
                self.discord_monitor = DiscordMonitor(self.on_ticket_detected)
                self.discord_loop.run_until_complete(self.discord_monitor.start_monitoring())
            except Exception as e:
                error_msg = str(e)
                logger.error(f"Discord monitoring error: {error_msg}")
                # Update GUI status on main thread
                if self.gui_overlay:
                    self.gui_overlay.root.after(0, lambda: self.gui_overlay.set_connection_status(False))
                    self.gui_overlay.root.after(0, lambda msg=error_msg: messagebox.showerror(
                        "Connection Error",
                        f"Failed to connect to Discord:\n{msg}\n\nPlease check your token and internet connection."
                    ))
        
        self.discord_thread = threading.Thread(target=run_discord, daemon=True)
        self.discord_thread.start()
        
        # Update connection status after a short delay
        def check_connection():
            if self.discord_monitor and self.discord_monitor.is_running:
                self.gui_overlay.set_connection_status(True)
            else:
                self.gui_overlay.set_connection_status(False)
        
        if self.gui_overlay:
            self.gui_overlay.root.after(3000, check_connection)  # Check after 3 seconds
    
    def stop_discord_monitor(self):
        """Stop Discord monitoring"""
        if self.discord_monitor and self.discord_loop:
            asyncio.run_coroutine_threadsafe(
                self.discord_monitor.stop_monitoring(), 
                self.discord_loop
            )
    
    def validate_configuration(self):
        """Validate configuration before starting"""
        if not config.DISCORD_TOKEN:
            messagebox.showerror("Configuration Error", "Discord token is not configured!")
            return False

        # Validate token format
        is_valid, message = config.validate_token(config.DISCORD_TOKEN)
        if not is_valid:
            messagebox.showerror("Token Error", f"Discord token validation failed:\n{message}")
            return False
        
        if not config.MONITORED_CHANNELS:
            response = messagebox.askyesno(
                "No Channels Configured", 
                "No specific channels are configured for monitoring.\n\n"
                "The bot will monitor ALL channels it has access to.\n\n"
                "Do you want to continue?"
            )
            if not response:
                return False
        
        return True
    
    def show_channel_info(self):
        """Show information about available channels"""
        def show_info():
            if self.discord_monitor and self.discord_monitor.client.guilds:
                channels_info = self.discord_monitor.get_monitored_channels_info()
                if channels_info:
                    info_text = "Monitored Channels:\n\n"
                    target_found = False
                    for channel in channels_info:
                        prefix = "🎯 " if channel.get('is_target', False) else "• "
                        info_text += f"{prefix}{channel['name']} in {channel['guild']} (ID: {channel['id']})\n"
                        if channel.get('is_target', False):
                            target_found = True

                    if hasattr(config, 'GUILD_ID') and not target_found:
                        info_text += f"\n⚠️ Target server (ID: {config.GUILD_ID}) not found!\n"
                        info_text += "Please add the bot to your Discord server using the invite link.\n"
                        info_text += "Run 'python generate_invite.py' to get the invite link."

                    messagebox.showinfo("Channel Information", info_text)
                else:
                    if hasattr(config, 'GUILD_ID'):
                        messagebox.showwarning("No Channels Found",
                            f"Bot is connected but target server (ID: {config.GUILD_ID}) not found.\n\n"
                            "Please:\n"
                            "1. Run 'python generate_invite.py' to get the invite link\n"
                            "2. Add the bot to your Discord server\n"
                            "3. Restart the application")
                    else:
                        messagebox.showinfo("Channel Information", "No channels available or bot not connected yet.")
            else:
                messagebox.showinfo("Channel Information", "Bot not connected yet. Please wait a moment and try again.")

        # Delay to allow connection to establish
        if self.gui_overlay:
            self.gui_overlay.root.after(3000, show_info)
    
    def run(self):
        """Run the main application"""
        logger.info("Starting Discord Ticket Monitor...")
        
        # Validate configuration
        if not self.validate_configuration():
            return
        
        try:
            # Create GUI overlay
            self.gui_overlay = TicketOverlay()
            
            # Override the close handler to properly shutdown
            original_on_closing = self.gui_overlay.on_closing
            def on_closing():
                self.stop_discord_monitor()
                original_on_closing()
            
            self.gui_overlay.on_closing = on_closing
            
            # Start Discord monitoring
            self.start_discord_monitor()
            
            # Show channel info after startup
            self.show_channel_info()
            
            # Start GUI main loop
            self.running = True
            self.gui_overlay.run()
            
        except KeyboardInterrupt:
            logger.info("Application interrupted by user")
        except Exception as e:
            logger.error(f"Application error: {e}")
            messagebox.showerror("Application Error", f"An error occurred: {str(e)}")
        finally:
            self.stop_discord_monitor()
            logger.info("Discord Ticket Monitor stopped")

def main():
    """Main entry point"""
    try:
        app = DiscordTicketMonitorApp()
        app.run()
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        messagebox.showerror("Fatal Error", f"A fatal error occurred: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
