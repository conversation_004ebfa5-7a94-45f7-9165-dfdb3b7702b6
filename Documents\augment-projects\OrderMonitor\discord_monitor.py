"""
Discord Channel Monitor - Core monitoring functionality
"""

import discord
from discord.ext import commands
import asyncio
import logging
from datetime import datetime
from typing import List, Callable, Optional
import config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DiscordMonitor:
    def __init__(self, on_ticket_callback: Callable = None):
        """
        Initialize the Discord monitor
        
        Args:
            on_ticket_callback: Function to call when a ticket message is found
        """
        # Set up Discord intents
        intents = discord.Intents.default()
        intents.message_content = True
        intents.guilds = True
        
        # Create bot client
        self.client = discord.Client(intents=intents)
        self.on_ticket_callback = on_ticket_callback
        self.is_running = False
        
        # Set up event handlers
        self.setup_events()
    
    def setup_events(self):
        """Set up Discord event handlers"""
        
        @self.client.event
        async def on_ready():
            logger.info(f'Discord monitor connected as {self.client.user}')
            self.is_running = True
            
            # Log available guilds and channels
            for guild in self.client.guilds:
                logger.info(f'Connected to guild: {guild.name} (ID: {guild.id})')
                for channel in guild.text_channels:
                    logger.info(f'  - Channel: #{channel.name} (ID: {channel.id})')
        
        @self.client.event
        async def on_message(self, message):
            # Ignore messages from the bot itself
            if message.author == self.client.user:
                return
            
            # Check if message is in a monitored channel
            if config.MONITORED_CHANNELS and message.channel.id not in config.MONITORED_CHANNELS:
                return
            
            # Check if message starts with ticket keyword
            if self.is_ticket_message(message.content):
                ticket_data = {
                    'content': message.content,
                    'author': str(message.author),
                    'channel': f"#{message.channel.name}",
                    'guild': message.guild.name if message.guild else "DM",
                    'timestamp': datetime.now().strftime("%H:%M:%S"),
                    'message_id': message.id,
                    'channel_id': message.channel.id
                }
                
                logger.info(f"Ticket detected: {ticket_data}")
                
                # Call the callback function if provided
                if self.on_ticket_callback:
                    self.on_ticket_callback(ticket_data)
    
    def is_ticket_message(self, content: str) -> bool:
        """
        Check if a message starts with the ticket keyword
        
        Args:
            content: Message content to check
            
        Returns:
            True if message starts with ticket keyword
        """
        if not content:
            return False
        
        keyword = config.TICKET_KEYWORD
        
        if config.CASE_SENSITIVE:
            return content.startswith(keyword)
        else:
            return content.lower().startswith(keyword.lower())
    
    async def start_monitoring(self):
        """Start the Discord monitoring"""
        try:
            await self.client.start(config.DISCORD_TOKEN)
        except discord.LoginFailure:
            logger.error("Failed to login to Discord. Check your token.")
            raise
        except Exception as e:
            logger.error(f"Error starting Discord monitor: {e}")
            raise
    
    async def stop_monitoring(self):
        """Stop the Discord monitoring"""
        if self.client and not self.client.is_closed():
            await self.client.close()
        self.is_running = False
    
    def get_monitored_channels_info(self) -> List[dict]:
        """Get information about monitored channels"""
        channels_info = []
        
        if not self.client.guilds:
            return channels_info
        
        for guild in self.client.guilds:
            for channel in guild.text_channels:
                if not config.MONITORED_CHANNELS or channel.id in config.MONITORED_CHANNELS:
                    channels_info.append({
                        'name': f"#{channel.name}",
                        'guild': guild.name,
                        'id': channel.id
                    })
        
        return channels_info
