# Discord Ticket Monitor

A desktop overlay application that monitors Discord channels for messages starting with "Ticket" and displays them in a compact, always-on-top window.

## Features

- 🎫 **Real-time Ticket Detection**: Monitors Discord channels for messages starting with "Ticket"
- 🖥️ **Desktop Overlay**: Compact, translucent window that stays on top of other applications
- 🔔 **Notifications**: Visual and audio alerts when new tickets are detected
- 🎨 **Customizable**: Dark/light themes, adjustable opacity, and positioning
- 📱 **Draggable Interface**: Move the overlay anywhere on your screen
- 🔍 **Channel Filtering**: Monitor specific channels or all accessible channels

## Installation

1. **Clone or download** this project to your computer

2. **Run the setup script**:
   ```bash
   python setup.py
   ```

3. **Configure your settings** in `config.py`:
   - Set your Discord bot token
   - Configure monitored channels (optional)
   - Adjust visual preferences

## Configuration

### Discord Setup ✅ CONFIGURED

**Current Configuration:**
- **Bot Token**: `MTQxMjU4NTE4NTU3MjI5MDU3MA.GjHMXe.ve9ltxXa7eCNNqBavOajL4eXfF6fLApSGlGoKM`
- **Client ID**: `1412585185572290570`
- **Target Server**: `Disguised Eats︱$8` (ID: 1407507935642259546)
- **Status**: ✅ Bot connected and server access confirmed

**Available Channels in Target Server:**
- `#🥳︱tickets` (ID: 1407516160508956682) - Perfect for ticket monitoring!
- `#🍕︱announcements` (ID: 1407510778583580826)
- `#🌯︱read-me` (ID: 1407511477618741308)
- And 19+ more channels

**Setup Steps:**
1. **Bot is already connected** ✅
2. **Find channel IDs** (optional):
   ```bash
   python channel_finder.py
   ```

### Channel Configuration

Edit `config.py` and modify the `MONITORED_CHANNELS` list:

```python
# Monitor specific channels
MONITORED_CHANNELS = [123456789012345678, 987654321098765432]

# Monitor all accessible channels (leave empty)
MONITORED_CHANNELS = []
```

### Visual Customization

Adjust these settings in `config.py`:

```python
# Window settings
WINDOW_WIDTH = 350
WINDOW_HEIGHT = 500
WINDOW_OPACITY = 0.95
ALWAYS_ON_TOP = True
DRAGGABLE = True

# Theme settings
DARK_THEME = True
SOUND_NOTIFICATIONS = True
VISUAL_NOTIFICATIONS = True
```

## Usage

1. **Start the application**:
   ```bash
   python main.py
   ```

2. **The overlay window will appear** showing:
   - Connection status (green/red dot)
   - Ticket counter
   - Real-time ticket messages

3. **Controls**:
   - **Clear**: Remove all displayed tickets
   - **−**: Minimize the window
   - **Drag**: Click and drag to reposition
   - **Close**: Right-click title bar or use window controls

## Ticket Detection

The application monitors for messages that start with "Ticket" (configurable):

- **Case-sensitive**: Set `CASE_SENSITIVE = True` in config.py
- **Case-insensitive**: Set `CASE_SENSITIVE = False` (default)

Examples of detected messages:
- "Ticket #12345 - Login issue"
- "ticket created for user support"
- "TICKET: Payment problem"

## Troubleshooting

### Connection Issues

1. **Check your Discord token** in `config.py`
2. **Verify internet connection**
3. **Check Discord API status**: https://discordstatus.com/

### No Tickets Appearing

1. **Verify the bot has access** to the channels you want to monitor
2. **Check channel IDs** in `MONITORED_CHANNELS` (if specified)
3. **Test with a simple message** starting with "Ticket"

### Performance Issues

1. **Limit monitored channels** to reduce message processing
2. **Reduce `MAX_DISPLAYED_TICKETS`** in config.py
3. **Disable sound notifications** if causing lag

## File Structure

```
OrderMonitor/
├── main.py              # Main application entry point
├── discord_monitor.py   # Discord API integration
├── gui_overlay.py       # GUI overlay interface
├── notifications.py     # Notification system
├── config.py           # Configuration settings
├── setup.py            # Installation script
├── requirements.txt    # Python dependencies
└── README.md          # This file
```

## Requirements

- Python 3.8+
- discord.py 2.3.0+
- tkinter (usually included with Python)
- plyer (optional, for system notifications)

## License

This project is provided as-is for educational and personal use.

## Support

If you encounter issues:

1. Check the `discord_monitor.log` file for error messages
2. Verify your Discord token and permissions
3. Ensure Python and dependencies are properly installed

---

**Note**: This application uses a Discord bot token. Keep your token secure and never share it publicly.
