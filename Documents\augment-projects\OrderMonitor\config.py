"""
Configuration file for Discord Channel Monitor
"""

# Discord Bot Token
DISCORD_TOKEN = "MTQwNDY3NTI3NjE0MjU0Mjg2OQ.GxBPH5.ZDsBfSF1my6V4qC7puwfPwl3PQpqW-e5x4ai4g"

# Monitoring Settings
TICKET_KEYWORD = "Ticket"  # Case-sensitive by default
CASE_SENSITIVE = False  # Set to True for case-sensitive matching

# GUI Settings
WINDOW_WIDTH = 350
WINDOW_HEIGHT = 500
WINDOW_OPACITY = 0.95
ALWAYS_ON_TOP = True
DRAGGABLE = True

# Theme Settings
DARK_THEME = True
BG_COLOR = "#2C2F33" if DARK_THEME else "#FFFFFF"
TEXT_COLOR = "#FFFFFF" if DARK_THEME else "#000000"
ACCENT_COLOR = "#7289DA"
TICKET_COLOR = "#43B581"

# Notification Settings
SOUND_NOTIFICATIONS = True
VISUAL_NOTIFICATIONS = True
MAX_DISPLAYED_TICKETS = 50

# Channels to monitor (add channel IDs here)
# Example: MONITORED_CHANNELS = [123456789012345678, 987654321098765432]
MONITORED_CHANNELS = []  # Will be configured later

# Position Settings
WINDOW_X = 50  # Distance from left edge of screen
WINDOW_Y = 50  # Distance from top edge of screen
